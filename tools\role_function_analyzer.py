#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Role.py函数调用分析工具
专门用于分析role.py中inner_content的函数调用，匹配Pet.js中的函数定义
"""

from pet_js_analyzer import PetJSAnalyzer
import json


class RoleFunctionAnalyzer:
    """Role.py函数调用分析器"""
    
    def __init__(self):
        self.pet_analyzer = PetJSAnalyzer()
        
        # 从role.py中提取的实际函数调用示例
        self.role_function_calls = [
            'p.addCM("右上框消息")',
            'p.initWorker()',
            'p.cls()',
            'p.offOpenWin("窗口ID")',
            'p.cps("zone")',
            'p.clsMes("npcChatReader")',
            'p._roomDesc("房间SN", "房间描述", "出口信息")',
            'p.addUser(用户ID, "用户HTML")',
            'p.delUser(用户ID)',
            'p.addRM("房间消息")',
            'p.addMessage("位置", "消息内容", 屏蔽标志)',
            'p.changePageState("状态")',
            'p.roomDesc("房间描述")',
            'p.setRoomText("房间文本")'
        ]
    
    def analyze_role_functions(self):
        """分析role.py中的函数调用"""
        print("=== Role.py中的函数调用分析 ===\n")
        
        results = {}
        
        for call_example in self.role_function_calls:
            analysis = self.pet_analyzer.analyze_line(call_example)
            
            if analysis['function_calls']:
                for call in analysis['function_calls']:
                    func_name = call['function_name']
                    
                    if func_name not in results:
                        results[func_name] = {
                            'function_name': func_name,
                            'parameters': call['function_definition'].get('params', []),
                            'description': call['function_definition'].get('description', ''),
                            'examples': []
                        }
                    
                    results[func_name]['examples'].append({
                        'call': call['raw_call'],
                        'param_values': call['param_values'],
                        'matched_params': call['matched_params']
                    })
        
        return results
    
    def print_analysis_results(self, results):
        """打印分析结果"""
        for func_name, info in sorted(results.items()):
            print(f"函数: {func_name}")
            print(f"参数: {info['parameters']}")
            if info['description']:
                print(f"描述: {info['description']}")
            
            print("调用示例:")
            for example in info['examples']:
                print(f"  {example['call']}")
                if example['matched_params']:
                    for param_name, param_value in example['matched_params'].items():
                        print(f"    {param_name}: {param_value}")
            print("-" * 60)
    
    def generate_parameter_extraction_code(self, results):
        """生成参数提取代码"""
        print("\n=== 参数提取代码生成 ===\n")
        
        code_template = '''
def extract_function_parameters(line_content):
    """从line_content中提取函数参数"""
    import re
    
    # 函数调用匹配结果
    extracted_params = {}
    
'''
        
        for func_name, info in sorted(results.items()):
            params = info['parameters']
            if params:
                param_pattern = ', '.join([f'({param})' for param in params])
                code_template += f'''    # {func_name}({', '.join(params)})
    if 'p.{func_name}(' in line_content:
        match = re.search(r'p\\.{func_name}\\s*\\(([^)]*)\\)', line_content)
        if match:
            param_str = match.group(1)
            # 解析参数值
            param_values = parse_parameters(param_str)
            if len(param_values) >= {len(params)}:
                extracted_params['{func_name}'] = {{
'''
                for i, param in enumerate(params):
                    code_template += f"                    '{param}': param_values[{i}],\n"
                code_template += "                }\n\n"
        
        code_template += '''    return extracted_params

def parse_parameters(param_str):
    """解析参数字符串"""
    if not param_str.strip():
        return []
    
    # 简单的参数分割逻辑
    params = []
    current_param = ""
    in_string = False
    string_char = None
    
    for char in param_str:
        if char in ['"', "'"] and not in_string:
            in_string = True
            string_char = char
            current_param += char
        elif char == string_char and in_string:
            in_string = False
            string_char = None
            current_param += char
        elif char == ',' and not in_string:
            params.append(current_param.strip())
            current_param = ""
        else:
            current_param += char
    
    if current_param.strip():
        params.append(current_param.strip())
    
    return params
'''
        
        return code_template
    
    def save_results_to_json(self, results, filename="role_function_analysis.json"):
        """保存分析结果到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"分析结果已保存到: {filename}")


def main():
    """主函数"""
    analyzer = RoleFunctionAnalyzer()
    
    # 分析函数调用
    results = analyzer.analyze_role_functions()
    
    # 打印结果
    analyzer.print_analysis_results(results)
    
    # 生成参数提取代码
    extraction_code = analyzer.generate_parameter_extraction_code(results)
    print(extraction_code)
    
    # 保存结果
    analyzer.save_results_to_json(results)
    
    print(f"\n总共分析了 {len(results)} 个函数")


if __name__ == "__main__":
    main()
