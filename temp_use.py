from tools.general import get_yzm
from role import *

if __name__ == '__main__':
    config1 = {
        "服务器":"test",
        "账号":"dayux12yy1",
        "密码":"000000",
        "角色序号":"0",
        "代理配置":{
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        }
    }
    config2 = {
        "服务器":"test",
        "账号":"dayux12yy2",
        "密码":"000000",
        "角色序号":"0",
        # "代理配置":{
        #     "代理账号":"FQTUENGM",
        #     "代理密码":"35B7E37DAF97"
        # }
    }

# {{ AURA-X: Add - 添加信号处理和主循环，让程序持续运行. Confirmed via 寸止 }}
import signal
import time

# 全局标志，用于控制程序退出
running = True

def signal_handler(signum, frame):
    """信号处理函数，优雅退出"""
    global running
    print(f"\n接收到信号 {signum}，正在优雅退出...")
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

try:
    obj1 = MYJ(config1)
    obj1.pull_online()
    obj2 = MYJ(config2)
    obj2.pull_online()
    print('obj1.cookies', obj1.cookies)
    print('obj2.cookies', obj2.cookies)
    print("程序已启动，按 Ctrl+C 退出...")

    # 主循环，保持程序运行
    while running:
        # for i in (obj1,obj2):
        #     i.send_single_order('talk 0')
        #     i.send_single_order('use fitems.experience.ExpRollLv1')
        time.sleep(0.28)  # 每秒检查一次退出标志

except KeyboardInterrupt:

    print("\n程序被用户中断")
except Exception as e:
    print(f"程序运行出错: {e}")
finally:
    print("程序已退出")