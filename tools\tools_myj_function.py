#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pet.js函数分析与参数提取工具
合并版本 - 包含完整的Pet.js函数分析和参数提取功能
"""

import re
import json
from typing import Dict, List, Tuple, Optional, Any


class PetJSFunctionAnalyzer:
    """Pet.js函数分析与参数提取工具"""
    
    def __init__(self, pet_js_path: str = "resources/Pet.js"):
        self.pet_js_path = pet_js_path
        self.functions = {}
        self.content = ""
        self._load_and_analyze()
    
    def _load_and_analyze(self):
        """加载并分析Pet.js文件"""
        try:
            # 尝试多种编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(self.pet_js_path, 'r', encoding=encoding) as f:
                        self.content = f.read()
                    print(f"成功使用 {encoding} 编码读取文件")
                    break
                except UnicodeDecodeError:
                    continue
            
            if not self.content:
                print(f"警告: 无法解码文件 {self.pet_js_path}")
                return
            
            # 分析所有函数
            self._analyze_all_functions()
            
        except FileNotFoundError:
            print(f"警告: 找不到文件 {self.pet_js_path}")
        except Exception as e:
            print(f"加载Pet.js时出错: {e}")
    
    def _analyze_all_functions(self):
        """分析所有函数定义"""
        # 匹配函数定义的正则表达式
        function_pattern = r'function\s+(\w+)\s*\(([^)]*)\)\s*\{'
        
        matches = list(re.finditer(function_pattern, self.content))
        print(f"从Pet.js中找到 {len(matches)} 个函数定义")
        
        for match in matches:
            func_name = match.group(1)
            params_str = match.group(2).strip()
            
            # 解析参数
            if params_str:
                params = [p.strip() for p in params_str.split(',') if p.strip()]
            else:
                params = []
            
            # 获取函数在文件中的行号
            line_num = self.content[:match.start()].count('\n') + 1
            
            self.functions[func_name] = {
                'params': params,
                'param_count': len(params),
                'line_number': line_num,
                'raw_definition': match.group(0),
                'description': f'Pet.js函数 - 行号: {line_num}'
            }
    
    def search_functions_by_name(self, pattern: str) -> Dict:
        """根据名称模式搜索函数"""
        results = {}
        regex_pattern = re.compile(pattern, re.IGNORECASE)
        
        for func_name, info in self.functions.items():
            if regex_pattern.search(func_name):
                results[func_name] = info
        
        return results
    
    def get_functions_with_params(self) -> Dict:
        """获取有参数的函数"""
        return {name: info for name, info in self.functions.items() 
                if info['param_count'] > 0}
    
    def get_functions_without_params(self) -> Dict:
        """获取无参数的函数"""
        return {name: info for name, info in self.functions.items() 
                if info['param_count'] == 0}
    
    def list_all_functions(self, sort_by='name') -> List[Tuple[str, Dict]]:
        """列出所有函数"""
        if sort_by == 'name':
            return sorted(self.functions.items())
        elif sort_by == 'param_count':
            return sorted(self.functions.items(), key=lambda x: x[1]['param_count'])
        elif sort_by == 'line_number':
            return sorted(self.functions.items(), key=lambda x: x[1]['line_number'])
        else:
            return list(self.functions.items())
    
    def get_function_statistics(self) -> Dict[str, int]:
        """获取函数统计信息"""
        stats = {
            'total_functions': len(self.functions),
            'functions_with_params': 0,
            'functions_without_params': 0
        }
        
        param_count_distribution = {}
        
        for func_name, info in self.functions.items():
            param_count = info['param_count']
            
            if param_count > 0:
                stats['functions_with_params'] += 1
            else:
                stats['functions_without_params'] += 1
            
            if param_count not in param_count_distribution:
                param_count_distribution[param_count] = 0
            param_count_distribution[param_count] += 1
        
        stats['param_count_distribution'] = param_count_distribution
        return stats
    
    def parse_parameters(self, param_str: str) -> List[str]:
        """解析参数字符串"""
        if not param_str.strip():
            return []
        
        params = []
        current_param = ""
        in_string = False
        string_char = None
        paren_level = 0
        bracket_level = 0
        
        for char in param_str:
            if char in ['"', "'"] and not in_string:
                in_string = True
                string_char = char
                current_param += char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
                current_param += char
            elif char == '(' and not in_string:
                paren_level += 1
                current_param += char
            elif char == ')' and not in_string:
                paren_level -= 1
                current_param += char
            elif char == '[' and not in_string:
                bracket_level += 1
                current_param += char
            elif char == ']' and not in_string:
                bracket_level -= 1
                current_param += char
            elif char == ',' and not in_string and paren_level == 0 and bracket_level == 0:
                params.append(current_param.strip())
                current_param = ""
            else:
                current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params
    
    def extract_function_parameters(self, line_content: str) -> Dict[str, Any]:
        """从line_content中提取函数参数"""
        extracted_params = {}
        
        # 匹配所有p.函数名(参数)的模式
        pattern = r'p\.(\w+)\s*\(([^)]*)\)'
        matches = re.finditer(pattern, line_content)
        
        for match in matches:
            func_name = match.group(1)
            param_str = match.group(2)
            
            if func_name in self.functions:
                func_def = self.functions[func_name]
                param_values = self.parse_parameters(param_str)
                
                # 将参数值与参数名匹配
                matched_params = {}
                for i, param_name in enumerate(func_def['params']):
                    if i < len(param_values):
                        matched_params[param_name] = param_values[i]
                
                extracted_params[func_name] = {
                    'raw_call': match.group(0),
                    'parameters': matched_params,
                    'param_values': param_values,
                    'description': func_def['description'],
                    'line_number': func_def['line_number']
                }
            else:
                # 未知函数也记录下来
                param_values = self.parse_parameters(param_str)
                extracted_params[func_name] = {
                    'raw_call': match.group(0),
                    'parameters': {f'param_{i}': val for i, val in enumerate(param_values)},
                    'param_values': param_values,
                    'description': '未知函数',
                    'line_number': 'unknown'
                }
        
        return extracted_params
    
    def clean_parameter_value(self, value: str) -> str:
        """清理参数值，去除引号等"""
        value = value.strip()
        if (value.startswith('"') and value.endswith('"')) or \
           (value.startswith("'") and value.endswith("'")):
            return value[1:-1]
        return value
    
    def process_line_content(self, line_content: str) -> Dict[str, Any]:
        """处理一行内容，返回结构化的参数信息"""
        extracted = self.extract_function_parameters(line_content)
        
        result = {
            'original_line': line_content,
            'functions_found': len(extracted),
            'function_calls': {}
        }
        
        for func_name, info in extracted.items():
            # 清理参数值
            cleaned_params = {}
            for param_name, param_value in info['parameters'].items():
                cleaned_params[param_name] = self.clean_parameter_value(param_value)
            
            result['function_calls'][func_name] = {
                'raw_call': info['raw_call'],
                'parameters': cleaned_params,
                'raw_parameters': info['parameters'],
                'description': info['description'],
                'line_number': info['line_number']
            }
        
        return result
    
    def get_function_info(self, func_name: str) -> Optional[Dict]:
        """获取指定函数的信息"""
        return self.functions.get(func_name)
    
    def export_functions_to_json(self, filename: str = "pet_js_functions.json"):
        """导出函数定义到JSON文件"""
        export_data = {
            'total_functions': len(self.functions),
            'statistics': self.get_function_statistics(),
            'functions': self.functions
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"函数定义已导出到: {filename}")
    
    def generate_function_summary(self) -> str:
        """生成函数摘要报告"""
        stats = self.get_function_statistics()
        total_functions = stats['total_functions']
        with_params = stats['functions_with_params']
        without_params = stats['functions_without_params']
        
        summary = f"""
=== Pet.js函数分析摘要 ===
总函数数量: {total_functions}
有参数函数: {with_params}
无参数函数: {without_params}

=== 按参数数量分组 ===
"""
        
        # 按参数数量分组
        param_groups = {}
        for name, info in self.functions.items():
            count = info['param_count']
            if count not in param_groups:
                param_groups[count] = []
            param_groups[count].append(name)
        
        for count in sorted(param_groups.keys()):
            functions = param_groups[count]
            summary += f"{count}个参数: {len(functions)}个函数\n"
            if count <= 3:  # 只显示参数较少的函数列表
                for func in sorted(functions)[:10]:  # 最多显示10个
                    params = ', '.join(self.functions[func]['params'])
                    summary += f"  {func}({params})\n"
                if len(functions) > 10:
                    summary += f"  ... 还有{len(functions)-10}个函数\n"
        
        return summary


# 便捷函数
def analyze_pet_js_functions(pet_js_path: str = "resources/Pet.js") -> PetJSFunctionAnalyzer:
    """创建Pet.js函数分析器"""
    return PetJSFunctionAnalyzer(pet_js_path)


def extract_parameters_from_line(line_content: str, pet_js_path: str = "resources/Pet.js") -> Dict[str, Any]:
    """从一行内容中提取函数参数"""
    analyzer = PetJSFunctionAnalyzer(pet_js_path)
    return analyzer.process_line_content(line_content)


if __name__ == "__main__":
    # 示例用法
    analyzer = PetJSFunctionAnalyzer()
    
    # 显示统计信息
    print(analyzer.generate_function_summary())
    
    # 测试参数提取
    test_lines = [
        'p._onChangeSelect("-1")',
        'p.addCM("测试消息");p.cls()',
        'p.addMessage("chatReader", "消息内容", false)'
    ]
    
    print("\n=== 参数提取测试 ===")
    for line in test_lines:
        result = analyzer.process_line_content(line)
        print(f"\n输入: {line}")
        print(f"找到 {result['functions_found']} 个函数调用:")
        
        for func_name, call_info in result['function_calls'].items():
            print(f"  函数: {func_name} (行号: {call_info['line_number']})")
            for param_name, param_value in call_info['parameters'].items():
                print(f"    {param_name}: {param_value}")
