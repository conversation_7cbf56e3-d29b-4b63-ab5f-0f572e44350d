#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数提取工具
专门用于从role.py的inner_content中提取Pet.js函数的参数
"""

import re
import json
from typing import Dict, List, Any


class ParameterExtractor:
    """参数提取器"""
    
    def __init__(self):
        # 基于Pet.js分析的函数定义
        self.function_definitions = {
            'addCM': {
                'params': ['mes'],
                'description': '添加聊天消息到右上框'
            },
            'addRM': {
                'params': ['mes'],
                'description': '添加房间消息'
            },
            'addMessage': {
                'params': ['pos', 'mes', 'pingbi'],
                'description': '添加消息到指定位置'
            },
            'cls': {
                'params': [],
                'description': '清空房间内容'
            },
            'clsMes': {
                'params': ['s'],
                'description': '清空指定元素内容'
            },
            'initWorker': {
                'params': [],
                'description': '初始化Worker连接'
            },
            'cps': {
                'params': ['state'],
                'description': '改变页面状态'
            },
            'changePageState': {
                'params': ['state'],
                'description': '改变页面状态'
            },
            'offOpenWin': {
                'params': ['objWin'],
                'description': '关闭指定窗口'
            },
            'addUser': {
                'params': ['id', 'str'],
                'description': '添加用户到房间列表'
            },
            'delUser': {
                'params': ['id'],
                'description': '从房间列表删除用户'
            },
            '_roomDesc': {
                'params': ['thesn', 'desc', 'exit'],
                'description': '设置房间描述'
            },
            'roomDesc': {
                'params': ['str'],
                'description': '设置房间文本'
            },
            'setRoomText': {
                'params': ['s'],
                'description': '设置房间文本内容'
            }
        }
    
    def parse_parameters(self, param_str: str) -> List[str]:
        """解析参数字符串"""
        if not param_str.strip():
            return []
        
        params = []
        current_param = ""
        in_string = False
        string_char = None
        paren_level = 0
        
        for char in param_str:
            if char in ['"', "'"] and not in_string:
                in_string = True
                string_char = char
                current_param += char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
                current_param += char
            elif char == '(' and not in_string:
                paren_level += 1
                current_param += char
            elif char == ')' and not in_string:
                paren_level -= 1
                current_param += char
            elif char == ',' and not in_string and paren_level == 0:
                params.append(current_param.strip())
                current_param = ""
            else:
                current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params
    
    def extract_function_parameters(self, line_content: str) -> Dict[str, Any]:
        """从line_content中提取函数参数"""
        extracted_params = {}
        
        # 匹配所有p.函数名(参数)的模式
        pattern = r'p\.(\w+)\s*\(([^)]*)\)'
        matches = re.finditer(pattern, line_content)
        
        for match in matches:
            func_name = match.group(1)
            param_str = match.group(2)
            
            if func_name in self.function_definitions:
                func_def = self.function_definitions[func_name]
                param_values = self.parse_parameters(param_str)
                
                # 将参数值与参数名匹配
                matched_params = {}
                for i, param_name in enumerate(func_def['params']):
                    if i < len(param_values):
                        matched_params[param_name] = param_values[i]
                
                extracted_params[func_name] = {
                    'raw_call': match.group(0),
                    'parameters': matched_params,
                    'param_values': param_values,
                    'description': func_def['description']
                }
        
        return extracted_params
    
    def clean_parameter_value(self, value: str) -> str:
        """清理参数值，去除引号等"""
        value = value.strip()
        if (value.startswith('"') and value.endswith('"')) or \
           (value.startswith("'") and value.endswith("'")):
            return value[1:-1]
        return value
    
    def process_line_content(self, line_content: str) -> Dict[str, Any]:
        """处理一行内容，返回结构化的参数信息"""
        extracted = self.extract_function_parameters(line_content)
        
        result = {
            'original_line': line_content,
            'functions_found': len(extracted),
            'function_calls': {}
        }
        
        for func_name, info in extracted.items():
            # 清理参数值
            cleaned_params = {}
            for param_name, param_value in info['parameters'].items():
                cleaned_params[param_name] = self.clean_parameter_value(param_value)
            
            result['function_calls'][func_name] = {
                'raw_call': info['raw_call'],
                'parameters': cleaned_params,
                'raw_parameters': info['parameters'],
                'description': info['description']
            }
        
        return result


def main():
    """测试函数"""
    extractor = ParameterExtractor()
    
    # 测试用例 - 基于role.py中实际出现的函数调用
    test_cases = [
        'p.addCM("测试右上框消息")',
        'p.cls()',
        'p.clsMes("npcChatReader")',
        'p.initWorker()',
        'p.cps("zone")',
        'p.offOpenWin("petInfoWin")',
        'p.addUser(12345, "<dt>用户HTML内容</dt>")',
        'p.delUser(12345)',
        'p._roomDesc("room001", "这是房间描述", "north,south,east")',
        'p.addRM("房间消息内容")',
        # 复杂的多函数调用
        'p.cls();p.addCM("清空后的消息");p.cps("zone")'
    ]
    
    print("=== 参数提取测试结果 ===\n")
    
    for i, test_line in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_line}")
        result = extractor.process_line_content(test_line)
        
        print(f"找到 {result['functions_found']} 个函数调用:")
        
        for func_name, call_info in result['function_calls'].items():
            print(f"  函数: {func_name}")
            print(f"  描述: {call_info['description']}")
            print(f"  参数:")
            for param_name, param_value in call_info['parameters'].items():
                print(f"    {param_name}: {param_value}")
        
        print("-" * 60)
    
    # 演示如何在实际代码中使用
    print("\n=== 实际使用示例 ===")
    sample_line = 'p.addCM("玩家张三进入了房间");p.addUser(123, "<dt>张三</dt>")'
    result = extractor.process_line_content(sample_line)
    
    print(f"处理内容: {sample_line}")
    print("提取的参数可用于数据处理:")
    
    for func_name, call_info in result['function_calls'].items():
        if func_name == 'addCM':
            message = call_info['parameters']['mes']
            print(f"  聊天消息: {message}")
        elif func_name == 'addUser':
            user_id = call_info['parameters']['id']
            user_html = call_info['parameters']['str']
            print(f"  用户ID: {user_id}")
            print(f"  用户HTML: {user_html}")


if __name__ == "__main__":
    main()
