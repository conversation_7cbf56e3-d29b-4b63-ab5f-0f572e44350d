import random
import json
import base64
from tools.default_setting import yzm_service_url
import requests

def get_yzm(proxies=None):
    """
    return X-TEMP_INT
    """
    # 取随机验证码尾数
    while True:
        temp_int = random.randint(2222, 8888)
        # 获取随机验证码
        # requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed=2345',proxies=None,timeout=5).content
        try:
            yzm_data = requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed={temp_int}', proxies=None, timeout=5).content
        # 打码自己的服务器获取登录验证码
            response = requests.post(yzm_service_url, data=base64.b64encode(yzm_data), timeout=5)
        # 组合计算结果以及原始验证码尾数
            _res = json.loads(response.text).get('res')
            return str(_res) + str(temp_int)
        except:
            continue

def unlock_status(encoded_str: str):
    ascii_values = [int(s) for s in encoded_str.split('O') if s]
    decoded_str = ''.join(chr(i) for i in ascii_values)
    return decoded_str[::-1]
