{"addCM": {"function_name": "addCM", "parameters": ["mes"], "description": "", "examples": [{"call": "p.addCM(\"右上框消息\")", "param_values": ["\"右上框消息\""], "matched_params": {"mes": "\"右上框消息\""}}]}, "initWorker": {"function_name": "initWorker", "parameters": [], "description": "", "examples": [{"call": "p<PERSON><PERSON>()", "param_values": [], "matched_params": {}}]}, "cls": {"function_name": "cls", "parameters": [], "description": "", "examples": [{"call": "p.cls()", "param_values": [], "matched_params": {}}]}, "offOpenWin": {"function_name": "offOpenWin", "parameters": ["objWin"], "description": "", "examples": [{"call": "p.off<PERSON>(\"窗口ID\")", "param_values": ["\"窗口ID\""], "matched_params": {"objWin": "\"窗口ID\""}}]}, "cps": {"function_name": "cps", "parameters": ["state"], "description": "", "examples": [{"call": "p.cps(\"zone\")", "param_values": ["\"zone\""], "matched_params": {"state": "\"zone\""}}]}, "clsMes": {"function_name": "clsMes", "parameters": ["s"], "description": "", "examples": [{"call": "p.cls<PERSON><PERSON>(\"npcChatReader\")", "param_values": ["\"npcChatReader\""], "matched_params": {"s": "\"npcChatReader\""}}]}, "_roomDesc": {"function_name": "_roomDesc", "parameters": ["thesn", "desc", "exit"], "description": "", "examples": [{"call": "p._roomDesc(\"房间SN\", \"房间描述\", \"出口信息\")", "param_values": ["\"房间SN\"", "\"房间描述\"", "\"出口信息\""], "matched_params": {"thesn": "\"房间SN\"", "desc": "\"房间描述\"", "exit": "\"出口信息\""}}]}, "addUser": {"function_name": "addUser", "parameters": ["id", "str"], "description": "", "examples": [{"call": "p.addUser(用户ID, \"用户HTML\")", "param_values": ["用户ID", "\"用户HTML\""], "matched_params": {"id": "用户ID", "str": "\"用户HTML\""}}]}, "delUser": {"function_name": "<PERSON><PERSON><PERSON>", "parameters": ["id"], "description": "", "examples": [{"call": "p<PERSON>(用户ID)", "param_values": ["用户ID"], "matched_params": {"id": "用户ID"}}]}, "addRM": {"function_name": "addRM", "parameters": ["mes"], "description": "", "examples": [{"call": "p.addRM(\"房间消息\")", "param_values": ["\"房间消息\""], "matched_params": {"mes": "\"房间消息\""}}]}, "addMessage": {"function_name": "addMessage", "parameters": ["pos", "mes", "pingbi"], "description": "", "examples": [{"call": "p.addMessage(\"位置\", \"消息内容\", 屏蔽标志)", "param_values": ["\"位置\"", "\"消息内容\"", "屏蔽标志"], "matched_params": {"pos": "\"位置\"", "mes": "\"消息内容\"", "pingbi": "屏蔽标志"}}]}, "changePageState": {"function_name": "changePageState", "parameters": ["state"], "description": "", "examples": [{"call": "p.changePageState(\"状态\")", "param_values": ["\"状态\""], "matched_params": {"state": "\"状态\""}}]}, "roomDesc": {"function_name": "roomDesc", "parameters": ["str"], "description": "", "examples": [{"call": "p.roomDesc(\"房间描述\")", "param_values": ["\"房间描述\""], "matched_params": {"str": "\"房间描述\""}}]}, "setRoomText": {"function_name": "setRoomText", "parameters": ["s"], "description": "", "examples": [{"call": "p.setRoomText(\"房间文本\")", "param_values": ["\"房间文本\""], "matched_params": {"s": "\"房间文本\""}}]}}