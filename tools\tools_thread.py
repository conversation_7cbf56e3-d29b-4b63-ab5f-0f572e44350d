# -*- coding: utf-8 -*-
import threading
import time
from enum import Enum


class ThreadState(Enum):
    """线程状态枚举"""
    CREATED = "created"      # 已创建
    RUNNING = "running"      # 运行中
    PAUSED = "paused"        # 已暂停
    STOPPED = "stopped"      # 已停止
    DESTROYED = "destroyed"  # 已销毁


class ControllableThread:
    """可控制的线程类，支持创建、运行、暂停、继续、销毁操作"""
    
    def __init__(self, target=None, args=(), kwargs=None, daemon=True, name=None):
        """
        初始化线程
        
        :param target: 目标函数
        :param args: 位置参数元组
        :param kwargs: 关键字参数字典
        :param daemon: 是否为守护线程
        :param name: 线程名称
        """
        self.target = target
        self.args = args
        self.kwargs = kwargs or {}
        self.daemon = daemon
        self.name = name
        
        # 线程控制标志
        self._pause_event = threading.Event()
        self._stop_event = threading.Event()
        self._destroy_event = threading.Event()
        
        # 初始状态为运行（不暂停）
        self._pause_event.set()
        
        # 线程状态
        self._state = ThreadState.CREATED
        self._thread = None
        self._lock = threading.Lock()
    
    def _run_wrapper(self):
        """线程运行包装器"""
        try:
            # 使用锁保护状态检查
            with self._lock:
                if self._stop_event.is_set() or self._destroy_event.is_set():
                    return

            # 检查是否需要暂停
            self._pause_event.wait()

            # 再次检查销毁标志（加锁保护）
            with self._lock:
                if self._destroy_event.is_set() or self._stop_event.is_set():
                    return

            # 执行目标函数
            if self.target:
                self.target(*self.args, **self.kwargs)

        except Exception as e:
            print(f"线程 {self.name} 执行异常: {e}")
        finally:
            with self._lock:
                if self._state != ThreadState.DESTROYED:
                    self._state = ThreadState.STOPPED
    
    def start(self):
        """启动线程"""
        with self._lock:
            if self._state != ThreadState.CREATED:
                raise RuntimeError(f"线程状态错误: {self._state.value}")
            
            self._thread = threading.Thread(
                target=self._run_wrapper,
                daemon=self.daemon,
                name=self.name
            )
            self._thread.start()
            self._state = ThreadState.RUNNING
            return True
    
    def pause(self):
        """暂停线程"""
        with self._lock:
            if self._state == ThreadState.RUNNING:
                self._pause_event.clear()
                self._state = ThreadState.PAUSED
                return True
            return False
    
    def resume(self):
        """继续线程"""
        with self._lock:
            if self._state == ThreadState.PAUSED:
                self._pause_event.set()
                self._state = ThreadState.RUNNING
                return True
            return False
    
    def stop(self):
        """停止线程"""
        with self._lock:
            if self._state in [ThreadState.RUNNING, ThreadState.PAUSED]:
                self._stop_event.set()
                self._pause_event.set()  # 确保线程不会卡在暂停状态
                self._state = ThreadState.STOPPED
                return True
            return False
    
    def destroy(self):
        """销毁线程"""
        with self._lock:
            if self._state == ThreadState.DESTROYED:
                return True

            self._destroy_event.set()
            self._pause_event.set()  # 确保线程不会卡在暂停状态
            self._state = ThreadState.DESTROYED

            if self._thread and self._thread.is_alive():
                # 等待线程结束，最多等待5秒
                self._thread.join(timeout=5)

            # 清理资源
            self._stop_event.clear()
            self._destroy_event.clear()
            self._pause_event.clear()

            return True
    
    def is_alive(self):
        """检查线程是否存活"""
        return self._thread and self._thread.is_alive()
    
    def get_state(self):
        """获取线程状态"""
        return self._state
    
    def join(self, timeout=None):
        """等待线程结束"""
        if self._thread:
            self._thread.join(timeout)
    
    def __str__(self):
        return f"ControllableThread(name={self.name}, state={self._state.value})"
    
    def __repr__(self):
        return self.__str__()


class LoopThread(ControllableThread):
    """循环执行的线程类"""
    
    def __init__(self, target=None, args=(), kwargs=None, daemon=True, name=None, interval=1):
        """
        初始化循环线程
        
        :param interval: 循环间隔时间（秒）
        """
        super().__init__(target, args, kwargs, daemon, name)
        self.interval = interval
    
    def _run_wrapper(self):
        """循环线程运行包装器"""
        try:
            while not self._destroy_event.is_set():
                # 使用锁保护状态检查
                with self._lock:
                    if self._stop_event.is_set():
                        break

                # 检查是否需要暂停
                self._pause_event.wait()

                # 再次检查销毁标志（加锁保护）
                with self._lock:
                    if self._destroy_event.is_set() or self._stop_event.is_set():
                        break

                # 执行目标函数
                if self.target:
                    self.target(*self.args, **self.kwargs)

                # 等待间隔时间，同时响应控制信号（0.05秒精度检测）
                start_time = time.time()
                while time.time() - start_time < self.interval:
                    if self._destroy_event.is_set() or self._stop_event.is_set():
                        break
                    # 在等待期间也要检查暂停状态
                    if not self._pause_event.is_set():
                        self._pause_event.wait()
                    time.sleep(0.05)  # 0.05秒检测间隔

        except Exception as e:
            print(f"循环线程 {self.name} 执行异常: {e}")
        finally:
            with self._lock:
                if self._state != ThreadState.DESTROYED:
                    self._state = ThreadState.STOPPED


if __name__ == "__main__":
    # 示例函数1：简单的打印函数
    def simple_task(name, count):
        for i in range(count):
            print(f"{name} 执行第 {i+1} 次")
            time.sleep(0.5)
        print(f"{name} 任务完成！")

    # 示例函数2：循环任务
    def loop_task(task_name):
        print(f"[{time.strftime('%H:%M:%S')}] {task_name} 正在执行...")

    print("=== 线程控制器示例程序 ===\n")

    # 示例1：普通线程控制
    print("1. 创建普通线程...")
    thread1 = ControllableThread(
        target=simple_task,
        args=("线程1", 10),
        name="SimpleThread"
    )

    print(f"线程状态: {thread1.get_state().value}")

    # 启动线程
    print("2. 启动线程...")
    thread1.start()
    print(f"线程状态: {thread1.get_state().value}")

    # 让线程运行一会儿
    time.sleep(2)

    # 暂停线程
    print("3. 暂停线程...")
    thread1.pause()
    print(f"线程状态: {thread1.get_state().value}")

    time.sleep(2)

    # 继续线程
    print("4. 继续线程...")
    thread1.resume()
    print(f"线程状态: {thread1.get_state().value}")

    # 等待线程完成
    thread1.join()
    print(f"线程最终状态: {thread1.get_state().value}\n")




    # 示例2：循环线程控制
    print("5. 创建循环线程...")
    loop_thread = LoopThread(
        target=loop_task,
        args=("循环任务",),
        interval=1,  # 每秒执行一次
        name="LoopThread"
    )

    # 启动循环线程
    print("6. 启动循环线程...")
    loop_thread.start()

    # 运行5秒
    time.sleep(5)

    # 暂停循环线程
    print("7. 暂停循环线程...")
    loop_thread.pause()
    print("暂停2秒...")
    time.sleep(2)

    # 继续循环线程
    print("8. 继续循环线程...")
    loop_thread.resume()
    time.sleep(3)

    # 停止循环线程
    print("9. 停止循环线程...")
    loop_thread.stop()
    time.sleep(1)

    # 销毁线程
    print("10. 销毁线程...")
    loop_thread.destroy()
    print(f"循环线程最终状态: {loop_thread.get_state().value}")

    print("\n=== 示例程序结束 ===")
