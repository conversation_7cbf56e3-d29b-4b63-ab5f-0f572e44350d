#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pet.js函数参数分析工具
用于分析Pet.js中的函数定义，匹配line_content中的函数调用，提取参数
"""

import re
import json
from typing import Dict, List, Tuple, Optional


class PetJSAnalyzer:
    """Pet.js函数分析器"""
    
    def __init__(self, pet_js_path: str = "../resources/Pet.js"):
        self.pet_js_path = pet_js_path
        self.functions = {}  # 存储函数定义和参数信息
        self._load_functions()
    
    def _load_functions(self):
        """加载Pet.js中的函数定义"""
        try:
            # 尝试多种编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            content = None

            for encoding in encodings:
                try:
                    with open(self.pet_js_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                print(f"警告: 无法解码文件 {self.pet_js_path}")
                return

            # 解析函数定义
            self._parse_functions(content)

        except FileNotFoundError:
            print(f"警告: 找不到文件 {self.pet_js_path}")
        except Exception as e:
            print(f"加载Pet.js时出错: {e}")
    
    def _parse_functions(self, content: str):
        """解析JavaScript函数定义"""
        
        # 匹配函数定义的正则表达式
        function_pattern = r'function\s+(\w+)\s*\(([^)]*)\)\s*\{'
        
        matches = re.finditer(function_pattern, content)
        
        for match in matches:
            func_name = match.group(1)
            params_str = match.group(2).strip()
            
            # 解析参数
            if params_str:
                params = [p.strip() for p in params_str.split(',') if p.strip()]
            else:
                params = []
            
            # 获取函数体的开始位置，用于后续分析
            func_start = match.end()
            
            self.functions[func_name] = {
                'params': params,
                'param_count': len(params),
                'start_pos': func_start
            }
        
        # 手动添加一些特殊的函数定义（从代码分析得出）
        self._add_special_functions()
    
    def _add_special_functions(self):
        """添加特殊函数定义（基于Pet.js实际代码分析）"""
        special_functions = {
            # 消息相关函数
            'addMessage': {
                'params': ['pos', 'mes', 'pingbi'],
                'param_count': 3,
                'description': '添加消息到指定位置'
            },
            'addCM': {
                'params': ['mes'],
                'param_count': 1,
                'description': '添加聊天消息 - 调用addMessage("chatReader", mes)'
            },
            'addRM': {
                'params': ['mes'],
                'param_count': 1,
                'description': '添加房间消息 - 调用addMessage("roomReader", mes)'
            },
            'addMY': {
                'params': ['mes'],
                'param_count': 1,
                'description': '添加我的消息 - 调用addMessage("chatMyReader", mes)'
            },
            'addIM': {
                'params': ['mes'],
                'param_count': 1,
                'description': '添加信息消息 - 调用addMessage("infoReader", mes)'
            },

            # 清理函数
            'cls': {
                'params': [],
                'param_count': 0,
                'description': '清空房间内容 - 清空getOutReader().innerHTML'
            },
            'clsMes': {
                'params': ['s'],
                'param_count': 1,
                'description': '清空指定元素内容 - document.all(s).innerHTML=""'
            },

            # 系统函数
            'initWorker': {
                'params': [],
                'param_count': 0,
                'description': '初始化Worker连接 - 设置init=true并执行cmd("foo login")'
            },
            'cps': {
                'params': ['state'],
                'param_count': 1,
                'description': '改变页面状态 - 调用changePageState(state)'
            },
            'changePageState': {
                'params': ['state'],
                'param_count': 1,
                'description': '改变页面状态 - 设置petWin.pageState并处理UI'
            },

            # 窗口管理
            'offOpenWin': {
                'params': ['windowId'],
                'param_count': 1,
                'description': '关闭指定窗口'
            },

            # 用户管理
            'addUser': {
                'params': ['id', 'str'],
                'param_count': 2,
                'description': '添加用户到房间列表 - 如果id!=myId则添加到playerList'
            },
            'delUser': {
                'params': ['id'],
                'param_count': 1,
                'description': '从房间列表删除用户 - 从playerList中移除指定用户'
            },

            # 房间描述
            '_roomDesc': {
                'params': ['thesn', 'desc', 'exit'],
                'param_count': 3,
                'description': '设置房间描述 - 调用roomDesc(desc)'
            },
            'roomDesc': {
                'params': ['str'],
                'param_count': 1,
                'description': '设置房间文本 - 调用setRoomText(str)'
            },
            'setRoomText': {
                'params': ['s'],
                'param_count': 1,
                'description': '设置房间文本 - thisRoomText.innerHTML=s'
            }
        }
        
        for func_name, info in special_functions.items():
            if func_name not in self.functions:
                self.functions[func_name] = info
    
    def extract_function_call_params(self, line_content: str) -> List[Dict]:
        """从line_content中提取函数调用及其参数"""
        results = []
        
        # 匹配p.函数名(参数)的模式
        pattern = r'p\.(\w+)\s*\(([^)]*)\)'
        
        matches = re.finditer(pattern, line_content)
        
        for match in matches:
            func_name = match.group(1)
            params_str = match.group(2).strip()
            
            # 解析参数值
            param_values = self._parse_param_values(params_str)
            
            # 获取函数定义信息
            func_info = self.functions.get(func_name, {})
            
            result = {
                'function_name': func_name,
                'raw_call': match.group(0),
                'param_values': param_values,
                'param_count': len(param_values),
                'function_definition': func_info,
                'matched_params': self._match_params_to_definition(func_name, param_values)
            }
            
            results.append(result)
        
        return results
    
    def _parse_param_values(self, params_str: str) -> List[str]:
        """解析参数值字符串"""
        if not params_str.strip():
            return []
        
        # 简单的参数分割（处理字符串中的逗号）
        params = []
        current_param = ""
        in_string = False
        string_char = None
        paren_level = 0
        
        for char in params_str:
            if char in ['"', "'"] and not in_string:
                in_string = True
                string_char = char
                current_param += char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
                current_param += char
            elif char == '(' and not in_string:
                paren_level += 1
                current_param += char
            elif char == ')' and not in_string:
                paren_level -= 1
                current_param += char
            elif char == ',' and not in_string and paren_level == 0:
                params.append(current_param.strip())
                current_param = ""
            else:
                current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params
    
    def _match_params_to_definition(self, func_name: str, param_values: List[str]) -> Dict:
        """将参数值与函数定义匹配"""
        func_info = self.functions.get(func_name, {})
        param_names = func_info.get('params', [])
        
        matched = {}
        for i, value in enumerate(param_values):
            if i < len(param_names):
                matched[param_names[i]] = value
            else:
                matched[f'param_{i}'] = value
        
        return matched
    
    def get_function_info(self, func_name: str) -> Optional[Dict]:
        """获取指定函数的信息"""
        return self.functions.get(func_name)
    
    def list_all_functions(self) -> Dict:
        """列出所有已知函数"""
        return self.functions
    
    def analyze_line(self, line_content: str) -> Dict:
        """分析一行内容中的所有函数调用"""
        function_calls = self.extract_function_call_params(line_content)
        
        return {
            'line_content': line_content,
            'function_calls': function_calls,
            'total_calls': len(function_calls)
        }


def main():
    """测试函数"""
    analyzer = PetJSAnalyzer()

    # 基于role.py中实际出现的函数调用进行测试
    test_lines = [
        'p.cls()',
        'p.clsMes("npcChatReader")',
        'p.addCM("测试聊天消息")',
        'p.addRM("房间消息内容")',
        'p.initWorker()',
        'p.cps("zone")',
        'p.offOpenWin("petInfoWin")',
        'p.addUser(12345, "<dt>用户HTML内容</dt>")',
        'p.delUser(12345)',
        'p._roomDesc("room1", "房间描述", "exit1,exit2")'
    ]

    print("=== Pet.js函数分析结果 ===\n")

    for line in test_lines:
        result = analyzer.analyze_line(line)
        print(f"输入: {line}")

        if result['function_calls']:
            for call in result['function_calls']:
                print(f"  函数名: {call['function_name']}")
                print(f"  原始调用: {call['raw_call']}")
                print(f"  参数值: {call['param_values']}")
                print(f"  参数匹配: {call['matched_params']}")

                func_def = call['function_definition']
                if func_def:
                    print(f"  函数描述: {func_def.get('description', '无描述')}")
                    print(f"  期望参数: {func_def.get('params', [])}")
                else:
                    print(f"  警告: 未找到函数定义")
        else:
            print("  未找到函数调用")

        print("-" * 60)

    # 显示所有已知函数
    print("\n=== 所有已知函数列表 ===")
    all_functions = analyzer.list_all_functions()
    for func_name, info in sorted(all_functions.items()):
        params_str = ', '.join(info.get('params', []))
        desc = info.get('description', '无描述')
        print(f"{func_name}({params_str}) - {desc}")


def analyze_role_py_line(line_content: str):
    """分析role.py中的一行内容"""
    analyzer = PetJSAnalyzer()
    result = analyzer.analyze_line(line_content)

    print(f"分析内容: {line_content}")
    print(f"找到 {result['total_calls']} 个函数调用:")

    for call in result['function_calls']:
        print(f"\n函数: {call['function_name']}")
        print(f"参数: {call['matched_params']}")

        # 返回结构化数据供进一步处理
        yield {
            'function_name': call['function_name'],
            'parameters': call['matched_params'],
            'raw_values': call['param_values'],
            'description': call['function_definition'].get('description', '')
        }


if __name__ == "__main__":
    main()
